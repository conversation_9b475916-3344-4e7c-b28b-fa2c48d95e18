/*
  ======================================================================================
  MISSION FRESH - COLORS AND FONTS ONLY - HOLY RULE 0003 COMPLIANCE
  Apple-style elegance - Only ONE shade per color across entire app
  NO sizing, spacing, or layout properties allowed
  ======================================================================================
*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Border Radius - Apple Style Elegance */
    --radius: 0.75rem;

    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    /* Primary Brand Colors - ONLY ONE GREEN ALLOWED */
    --primary: 160 84.2% 39.4%;
    --primary-foreground: 0 0% 100%;
    
    /* Secondary Colors - ONLY ONE SHADE */
    --secondary: 0 0% 100%;
    --secondary-foreground: 222.2 84% 4.9%;
    --secondary-hover: 0 0% 96%;
    
    /* Muted Colors */
    --muted: 0 0% 98%;
    --muted-foreground: 215.4 16.3% 35%;
    --muted-subtle: 0 0% 98%;
    
    /* Accent Colors - ONLY ONE SHADE */
    --accent: 0 0% 100%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* Status Colors - ONLY ONE SHADE PER COLOR */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;
    --destructive-subtle: 0 84.2% 98%;
    --success: 160 84.2% 39.4%;
    --success-foreground: 0 0% 100%;
    --success-subtle: 160 84.2% 98%;
    --warning: 35 85% 55%;
    --warning-foreground: 0 0% 100%;
    --warning-subtle: 35 85% 98%;
    --info: 212 85% 60%;
    --info-foreground: 0 0% 100%;
    
    /* UI Element Colors */
    --border: 214.3 31.8% 91.4%;
    --input: 0 0% 100%;
    --ring: 160 84.2% 39.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    /* Chart Color - SINGLE BRAND GREEN ONLY */
    --chart: 160 84.2% 39.4%;
  }

  /* Font Character Styles Only */
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family: Inter, system-ui, -apple-system, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11", "ss01", "ss03";
  }

  /* Header Background Color Only */
  nav, header {
    background-color: hsl(var(--background));
  }

  /* UNIFIED BUTTON SYSTEM - ONLY BRAND GREEN ALLOWED */
  .btn-primary {
    background-color: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
  }

  .btn-primary:hover {
    background-color: hsl(var(--primary));
    opacity: 0.9;
  }

  .btn-primary:focus {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }
  
  .btn-secondary {
    background-color: hsl(var(--secondary));
    color: hsl(var(--secondary-foreground));
    border: 1px solid hsl(var(--border));
  }
  
  .btn-secondary:hover {
    background-color: hsl(var(--secondary-hover));
  }

  /* NAVIGATION LINK STYLES */
  .nav-link {
    transition: all 0.2s ease-in-out;
  }

  .nav-link:hover {
    color: hsl(var(--primary));
  }

  .nav-link:focus {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* CARD COMPONENT STYLES */
  .card {
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: 0.5rem;
    padding: 2.5rem;
    transition: box-shadow 0.2s ease-in-out;
  }

  .card:hover {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  }

  /* ICON CONTAINER STYLES */
  .icon-container {
    width: 3rem;
    height: 3rem;
    background-color: hsl(var(--primary));
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .icon-container-round {
    width: 4rem;
    height: 4rem;
    background-color: hsl(var(--card));
    border: 1px solid hsl(var(--border));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* TEXT COMPONENT STYLES */
  .heading-primary {
    font-size: 2.25rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    letter-spacing: -0.025em;
    margin-bottom: 1rem;
  }

  .heading-secondary {
    font-size: 1.25rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 0.75rem;
  }

  .text-muted {
    color: hsl(var(--muted-foreground));
    line-height: 1.625;
    margin-bottom: 1rem;
  }

  /* CONSISTENT SIZING SYSTEM */
  .size-input {
    height: 3.5rem; /* h-14 - Standard input height */
  }

  .size-button {
    height: 3.5rem; /* h-14 - Standard button height */
  }

  .size-button-large {
    height: 4rem; /* h-16 - Large button height */
  }

  .size-avatar {
    width: 3rem; /* w-12 */
    height: 3rem; /* h-12 */
  }

  .size-icon-large {
    width: 4rem; /* w-16 */
    height: 4rem; /* h-16 */
  }

  /* ACCESSIBILITY ENHANCEMENTS */
  .btn-primary:focus,
  .btn-secondary:focus {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  .card:focus-within {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
  }

  /* Skip to main content link */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
  }

  .skip-link:focus {
    top: 6px;
  }
}
