import { useState, useEffect } from 'react'
import { useSearchParams, Link, useNavigate } from 'react-router-dom'
import { Search, Filter, ArrowRight } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface SearchResult {
  id: string
  title: string
  description: string
  type: 'tool' | 'method' | 'product' | 'guide'
  url: string
  category?: string
}

export default function SearchPage() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const query = searchParams.get('q') || ''
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [searchInput, setSearchInput] = useState(query)
  const [selectedFilter, setSelectedFilter] = useState<string>('all')

  // HOLY RULE 1: Dynamic search results from database
  useEffect(() => {
    if (query) {
      performSearch(query)
    }
  }, [query])

  // Sync searchInput with URL query parameter
  useEffect(() => {
    setSearchInput(query)
  }, [query])

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) return

    setLoading(true)
    try {
      // Search across multiple tables for comprehensive results
      const [nrtProducts, quitMethods, tools] = await Promise.all([
        // Search NRT products
        supabase
          .from('nrt_products')
          .select('id, name, description, category')
          .or(`name.ilike.%${searchQuery}%, description.ilike.%${searchQuery}%, category.ilike.%${searchQuery}%`)
          .limit(10),
        
        // Search quit methods
        supabase
          .from('quit_methods')
          .select('id, name, description, category')
          .or(`name.ilike.%${searchQuery}%, description.ilike.%${searchQuery}%, category.ilike.%${searchQuery}%`)
          .limit(10),
        
        // Search static tools/pages (fallback to static data for tools)
        Promise.resolve({ data: getStaticToolsResults(searchQuery), error: null })
      ])

      const searchResults: SearchResult[] = []

      // Process NRT products
      if (nrtProducts.data) {
        nrtProducts.data.forEach(product => {
          searchResults.push({
            id: `nrt-${product.id}`,
            title: product.name,
            description: product.description,
            type: 'product',
            url: `/tools/nrt-products?product=${product.id}`,
            category: product.category
          })
        })
      }

      // Process quit methods
      if (quitMethods.data) {
        quitMethods.data.forEach(method => {
          searchResults.push({
            id: `method-${method.id}`,
            title: method.name,
            description: method.description,
            type: 'method',
            url: `/tools/quit-methods?method=${method.id}`,
            category: method.category
          })
        })
      }

      // Process static tools
      if (tools.data) {
        searchResults.push(...tools.data)
      }

      setResults(searchResults)
    } catch (error) {
      console.error('Search error:', error)
      // Fallback to static results
      setResults(getStaticToolsResults(searchQuery))
    } finally {
      setLoading(false)
    }
  }

  // HOLY RULE 12: Static tool information is acceptable as hardcoded content
  const getStaticToolsResults = (searchQuery: string): SearchResult[] => {
    const staticTools = [
      {
        id: 'nrt-guide',
        title: 'NRT Guide',
        description: 'Expert guidance and personalized recommendations for nicotine replacement therapy',
        type: 'guide' as const,
        url: '/tools/nrt-guide',
        category: 'Nicotine Replacement'
      },
      {
        id: 'smokeless-directory',
        title: 'Smokeless Directory',
        description: 'Comprehensive directory of smokeless alternatives with expert reviews',
        type: 'tool' as const,
        url: '/tools/smokeless-directory',
        category: 'Alternatives'
      },
      {
        id: 'quit-methods',
        title: 'Quitting Methods',
        description: 'Evidence-based strategies and step-by-step guides for smoking cessation',
        type: 'guide' as const,
        url: '/tools/quit-methods',
        category: 'Methods'
      },
      {
        id: 'calculators',
        title: 'Wellness Calculator',
        description: 'Track progress, calculate health improvements, and visualize your journey',
        type: 'tool' as const,
        url: '/tools/calculators',
        category: 'Tracking'
      },
      {
        id: 'holistic-health',
        title: 'Holistic Wellness',
        description: 'Comprehensive guides for improving sleep quality, energy levels, and mental focus',
        type: 'guide' as const,
        url: '/tools/holistic-health',
        category: 'Wellness'
      }
    ]

    return staticTools.filter(tool => 
      tool.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.category.toLowerCase().includes(searchQuery.toLowerCase())
    )
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchInput.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchInput)}`)
    }
  }

  const filteredResults = selectedFilter === 'all' 
    ? results 
    : results.filter(result => result.type === selectedFilter)

  return (
    <div className="min-h-screen bg-background">
      <div className="container max-w-4xl mx-auto px-6 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-semibold text-foreground mb-6">Search Results</h1>
          
          {/* Search Form */}
          <form onSubmit={handleSearch} className="mb-6">
            <div className="relative flex gap-3">
              <div className="flex-1 relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
                <input
                  type="search"
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  placeholder="Search tools, methods, or ask a question..."
                  className="w-full h-14 pl-12 pr-4 text-lg border border-border bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary rounded-xl"
                />
              </div>
              <button
                type="submit"
                className="h-14 px-6 bg-primary text-primary-foreground rounded-xl hover:bg-primary/90 transition-colors font-medium"
              >
                Search
              </button>
            </div>
          </form>

          {/* Filters */}
          <div className="flex items-center gap-4 mb-6">
            <Filter className="w-5 h-5 text-muted-foreground" />
            <div className="flex gap-2">
              {['all', 'tool', 'method', 'product', 'guide'].map((filter) => (
                <button
                  key={filter}
                  onClick={() => setSelectedFilter(filter)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    selectedFilter === filter
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-card text-card-foreground border border-border hover:bg-accent'
                  }`}
                >
                  {filter.charAt(0).toUpperCase() + filter.slice(1)}
                </button>
              ))}
            </div>
          </div>

          {query && (
            <p className="text-muted-foreground">
              Showing results for: <span className="font-medium text-foreground">"{query}"</span>
            </p>
          )}
        </div>

        {/* Results */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-muted-foreground">Searching...</p>
          </div>
        ) : filteredResults.length > 0 ? (
          <div className="space-y-4">
            {filteredResults.map((result) => (
              <Link
                key={result.id}
                to={result.url}
                className="block bg-card border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-200 group"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors">
                        {result.title}
                      </h3>
                      <span className="px-2 py-1 bg-accent text-accent-foreground text-xs rounded-md">
                        {result.type}
                      </span>
                      {result.category && (
                        <span className="px-2 py-1 bg-muted text-muted-foreground text-xs rounded-md">
                          {result.category}
                        </span>
                      )}
                    </div>
                    <p className="text-muted-foreground leading-relaxed">
                      {result.description}
                    </p>
                  </div>
                  <ArrowRight className="w-5 h-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-200 ml-4 flex-shrink-0" />
                </div>
              </Link>
            ))}
          </div>
        ) : query ? (
          <div className="text-center py-12">
            <Search className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">No results found</h3>
            <p className="text-muted-foreground mb-6">
              Try adjusting your search terms or browse our tools directly.
            </p>
            <Link
              to="/tools"
              className="btn-primary"
            >
              Browse All Tools
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        ) : (
          <div className="text-center py-12">
            <Search className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-foreground mb-2">Start your search</h3>
            <p className="text-muted-foreground">
              Enter a search term to find tools, methods, and resources.
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
