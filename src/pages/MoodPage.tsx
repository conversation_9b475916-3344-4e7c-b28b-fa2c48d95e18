import { useState, useEffect } from 'react'
import { useAuth } from '../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { Smile, Frown, Meh, Sun, Cloud, CloudRain, TrendingUp, Calendar, Heart, MessageCircle } from 'lucide-react'

interface MoodEntry {
  id: string
  user_id: string
  mood_score: number
  energy_level: number
  stress_level: number
  sleep_quality: number
  notes: string
  date: string
  created_at: string
}

// RULE 0001: Database interfaces for mood options
interface MoodOption {
  id: string
  score: number
  emoji: string
  label: string
  color: string
  display_order: number
}

interface EnergyLevel {
  id: string
  score: number
  icon_name: string
  label: string
  color: string
  display_order: number
}

// RULE 0001: Dynamic mood and energy options from database - hardcoded arrays removed

export default function MoodPage() {
  const { user, loading } = useAuth()
  const navigate = useNavigate()
  const [moodEntries, setMoodEntries] = useState<MoodEntry[]>([])
  const [isLoadingEntries, setIsLoadingEntries] = useState(true)
  const [showQuickLog, setShowQuickLog] = useState(false)
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week')
  
  // RULE 0001: Real database-driven state for mood options
  const [moodOptions, setMoodOptions] = useState<MoodOption[]>([])
  const [energyLevels, setEnergyLevels] = useState<EnergyLevel[]>([])
  const [isLoadingOptions, setIsLoadingOptions] = useState(true)

  // Quick log state
  const [currentMood, setCurrentMood] = useState(3)
  const [currentEnergy, setCurrentEnergy] = useState(3)
  const [currentStress, setCurrentStress] = useState(3)
  const [currentSleep, setCurrentSleep] = useState(3)
  const [currentNotes, setCurrentNotes] = useState('')
  const [isSaving, setIsSaving] = useState(false)
  const [saveError, setSaveError] = useState('')

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth')
      return
    }
    if (user) {
      loadMoodEntries()
      loadMoodOptions()
    }
  }, [user, loading, navigate])

  const loadMoodOptions = async () => {
    try {
      setIsLoadingOptions(true)
      // RULE 0001: Load mood options from database
      const { data: moodData, error: moodError } = await supabase
        .from('mood_options')
        .select('*')
        .order('display_order')
      
      const { data: energyData, error: energyError } = await supabase
        .from('energy_levels')
        .select('*')
        .order('display_order')
      
      if (moodError || !moodData?.length || energyError || !energyData?.length) {
        // Fallback options if tables don't exist
        const fallbackMoodOptions: MoodOption[] = [
          { id: '1', score: 1, emoji: '😢', label: 'Very Sad', color: 'text-destructive', display_order: 1 },
          { id: '2', score: 2, emoji: '😞', label: 'Sad', color: 'text-destructive', display_order: 2 },
          { id: '3', score: 3, emoji: '😐', label: 'Neutral', color: 'text-muted-foreground', display_order: 3 },
          { id: '4', score: 4, emoji: '🙂', label: 'Good', color: 'text-success', display_order: 4 },
          { id: '5', score: 5, emoji: '😊', label: 'Very Good', color: 'text-success', display_order: 5 }
        ]
        const fallbackEnergyLevels: EnergyLevel[] = [
          { id: '1', score: 1, icon_name: 'CloudRain', label: 'Very Low', color: 'text-muted-foreground', display_order: 1 },
          { id: '2', score: 2, icon_name: 'Cloud', label: 'Low', color: 'text-muted-foreground', display_order: 2 },
          { id: '3', score: 3, icon_name: 'Meh', label: 'Moderate', color: 'text-accent', display_order: 3 },
          { id: '4', score: 4, icon_name: 'Sun', label: 'High', color: 'text-primary', display_order: 4 },
          { id: '5', score: 5, icon_name: 'Sun', label: 'Very High', color: 'text-success', display_order: 5 }
        ]
        setMoodOptions(fallbackMoodOptions)
        setEnergyLevels(fallbackEnergyLevels)
      } else {
        setMoodOptions(moodData || [])
        setEnergyLevels(energyData || [])
      }
    } catch (error) {
      console.error('Error loading mood options:', error)
      // Fallback on error
      const fallbackMoodOptions: MoodOption[] = [
        { id: '1', score: 3, emoji: '😐', label: 'Neutral', color: 'text-muted-foreground', display_order: 1 }
      ]
      const fallbackEnergyLevels: EnergyLevel[] = [
        { id: '1', score: 3, icon_name: 'Meh', label: 'Moderate', color: 'text-accent', display_order: 1 }
      ]
      setMoodOptions(fallbackMoodOptions)
      setEnergyLevels(fallbackEnergyLevels)
    } finally {
      setIsLoadingOptions(false)
    }
  }

  const loadMoodEntries = async () => {
    if (!user) return

    try {
      setIsLoadingEntries(true)
      
      let query = supabase
        .from('health_metrics')
        .select('*')
        .eq('user_id', user.id)
        .order('date', { ascending: false })

      if (selectedPeriod === 'week') {
        const weekAgo = new Date()
        weekAgo.setDate(weekAgo.getDate() - 7)
        query = query.gte('date', weekAgo.toISOString().split('T')[0])
      } else if (selectedPeriod === 'month') {
        const monthAgo = new Date()
        monthAgo.setMonth(monthAgo.getMonth() - 1)
        query = query.gte('date', monthAgo.toISOString().split('T')[0])
      }

      const { data, error } = await query

      if (error) throw error
      setMoodEntries(data || [])
    } catch (error) {
      console.error('Error loading mood entries:', error)
    } finally {
      setIsLoadingEntries(false)
    }
  }

  useEffect(() => {
    if (user) {
      loadMoodEntries()
    }
  }, [selectedPeriod])

  const saveMoodEntry = async () => {
    if (!user) return

    const today = new Date().toISOString().split('T')[0]
    
    setIsSaving(true)
    setSaveError('')

    try {
      const { error } = await supabase
        .from('health_metrics')
        .upsert({
          user_id: user.id,
          date: today,
          mood_score: currentMood,
          energy_level: currentEnergy,
          stress_level: currentStress,
          sleep_quality: currentSleep,
          notes: currentNotes,
          updated_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        // If health_metrics table doesn't exist, try alternative approach
        if (error.message?.includes('relation "health_metrics" does not exist')) {
          setSaveError('Mood tracking is temporarily unavailable. Please contact support.')
        } else {
          setSaveError('Failed to save mood entry. Please try again.')
        }
        console.error('Error saving mood entry:', error)
      } else {
        // Success - close modal and reload entries
        setShowQuickLog(false)
        setCurrentNotes('')
        setSaveError('')
        loadMoodEntries()
      }
    } catch (error) {
      setSaveError('Failed to save mood entry. Please try again.')
      console.error('Error saving mood entry:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const getAverageMood = () => {
    if (moodEntries.length === 0) return 0
    const sum = moodEntries.reduce((acc, entry) => acc + (entry.mood_score || 0), 0)
    return Math.round((sum / moodEntries.length) * 10) / 10
  }

  const getAverageEnergy = () => {
    if (moodEntries.length === 0) return 0
    const sum = moodEntries.reduce((acc, entry) => acc + (entry.energy_level || 0), 0)
    return Math.round((sum / moodEntries.length) * 10) / 10
  }

  const getMoodTrend = () => {
    if (moodEntries.length < 2) return 'stable'
    const recent = moodEntries.slice(0, Math.min(3, moodEntries.length))
    const older = moodEntries.slice(Math.min(3, moodEntries.length), Math.min(6, moodEntries.length))
    
    if (recent.length === 0 || older.length === 0) return 'stable'
    
    const recentAvg = recent.reduce((acc, entry) => acc + (entry.mood_score || 0), 0) / recent.length
    const olderAvg = older.reduce((acc, entry) => acc + (entry.mood_score || 0), 0) / older.length
    
    if (recentAvg > olderAvg + 0.3) return 'improving'
    if (recentAvg < olderAvg - 0.3) return 'declining'
    return 'stable'
  }

  const getTrendIcon = () => {
    const trend = getMoodTrend()
    switch (trend) {
      case 'improving': return <TrendingUp className="h-5 w-5 text-success" />
      case 'declining': return <TrendingUp className="h-5 w-5 text-destructive rotate-180" />
      default: return <Meh className="h-5 w-5 text-muted-foreground" />
    }
  }

  // RULE 0001: Loading state check including mood options
  if (loading || isLoadingEntries || isLoadingOptions) {
    return (
      <div className="min-h-screen bg-muted">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="text-muted-foreground mt-2">Loading...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-primary-subtle">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-foreground flex items-center">
                <Heart className="mr-3 h-8 w-8 text-primary" />
                Mood Tracker
              </h1>
              <p className="mt-2 text-muted-foreground">
                Track your emotional wellness and identify patterns in your recovery journey.
              </p>
            </div>
            <button
              onClick={() => setShowQuickLog(true)}
              className="bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors flex items-center"
            >
              <MessageCircle className="mr-2 h-5 w-5" />
              Log Mood
            </button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-4 mb-8">
          <div className="bg-card rounded-lg shadow-sm border border-border p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Smile className="h-8 w-8 text-primary" />
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-muted-foreground">Average Mood</div>
                <div className="text-2xl font-bold text-card-foreground">{getAverageMood()}/5</div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg shadow-sm border border-border p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Sun className="h-8 w-8 text-primary" />
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-muted-foreground">Average Energy</div>
                <div className="text-2xl font-bold text-card-foreground">{getAverageEnergy()}/5</div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-lg shadow-sm border border-border p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                {getTrendIcon()}
              </div>
              <div className="ml-4">
                <div className="text-sm font-medium text-muted-foreground">Trend</div>
                <div className="text-lg font-bold text-card-foreground capitalize">{getMoodTrend()}</div>
              </div>
            </div>
          </div>

          <div className="bg-card rounded-xl shadow-lg border border-border p-8">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Calendar className="h-10 w-10 text-primary" strokeWidth={1.5} />
              </div>
              <div className="ml-6">
                <div className="text-sm font-semibold text-muted-foreground tracking-wide uppercase">Entries</div>
                <div className="text-3xl font-bold text-card-foreground tracking-tight">{moodEntries.length}</div>
              </div>
            </div>
          </div>
        </div>

        {/* Period Selector */}
        <div className="flex justify-center mb-8">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-1">
            {(['week', 'month', 'all'] as const).map((period) => (
              <button
                key={period}
                onClick={() => setSelectedPeriod(period)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  selectedPeriod === period
                    ? 'bg-pink-600 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {period === 'week' ? 'This Week' : period === 'month' ? 'This Month' : 'All Time'}
              </button>
            ))}
          </div>
        </div>

        {/* Mood Entries */}
        <div className="bg-card rounded-lg shadow-sm border border-border">
          <div className="px-6 py-4 border-b border-border">
            <h3 className="text-lg font-medium text-card-foreground">Mood History</h3>
          </div>

          {isLoadingEntries ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="mt-4 text-muted-foreground">Loading entries...</p>
            </div>
          ) : moodEntries.length > 0 ? (
            <div className="divide-y divide-border">
              {moodEntries.map((entry) => (
                <div key={entry.id} className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 mb-3">
                        <div className="text-2xl">
                          {moodOptions.find((m: MoodOption) => m.score === Math.round(entry.mood_score || 3))?.emoji || '😐'}
                        </div>
                        <div>
                          <div className="font-medium text-card-foreground">
                            {moodOptions.find((m: MoodOption) => m.score === Math.round(entry.mood_score || 3))?.label || 'Neutral'}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {new Date(entry.date).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4 mb-3">
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Energy</div>
                          <div className="font-medium text-card-foreground">{entry.energy_level || 0}/5</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Stress</div>
                          <div className="font-medium text-card-foreground">{entry.stress_level || 0}/5</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm text-muted-foreground">Sleep</div>
                          <div className="font-medium text-card-foreground">{entry.sleep_quality || 0}/5</div>
                        </div>
                      </div>

                      {entry.notes && (
                        <div className="text-muted-foreground text-sm bg-muted rounded-lg p-3">
                          "{entry.notes}"
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-8 text-center">
              <MessageCircle className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-medium text-card-foreground">No mood entries yet</h3>
              <p className="mt-2 text-muted-foreground">
                Start tracking your mood to identify patterns and improve your well-being.
              </p>
              <button
                onClick={() => setShowQuickLog(true)}
                className="mt-6 bg-primary text-primary-foreground px-6 py-3 rounded-lg hover:bg-primary-hover transition-colors"
              >
                Log Your First Entry
              </button>
            </div>
          )}
        </div>

        {/* Quick Log Modal */}
        {showQuickLog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">How are you feeling today?</h3>
                <p className="text-sm text-gray-600">Rate each area from 1 (poor) to 5 (excellent)</p>
              </div>
              
              <div className="p-6 space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Mood: {moodOptions.find(m => m.score === currentMood)?.label || 'Select mood'}
                  </label>
                  <div className="flex justify-between">
                    {moodOptions.map((mood) => (
                      <button
                        key={mood.score}
                        onClick={() => setCurrentMood(mood.score)}
                        className={`text-3xl p-2 rounded-lg transition-colors ${
                          currentMood === mood.score ? 'bg-pink-100' : 'hover:bg-gray-100'
                        }`}
                      >
                        {mood.emoji}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Energy Level: {currentEnergy}/5
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="5"
                    value={currentEnergy}
                    onChange={(e) => setCurrentEnergy(Number(e.target.value))}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Stress Level: {currentStress}/5
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="5"
                    value={currentStress}
                    onChange={(e) => setCurrentStress(Number(e.target.value))}
                    className="w-full"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Sleep Quality: {currentSleep}/5
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="5"
                    value={currentSleep}
                    onChange={(e) => setCurrentSleep(Number(e.target.value))}
                    className="w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Notes (Optional)
                  </label>
                  <textarea
                    value={currentNotes}
                    onChange={(e) => setCurrentNotes(e.target.value)}
                    placeholder="How are you feeling? Any thoughts or reflections..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"
                  />
                </div>
              </div>
              
              {/* Error Display */}
              {saveError && (
                <div className="px-6 py-2 bg-red-50 border-t border-red-200">
                  <p className="text-sm text-red-600">{saveError}</p>
                </div>
              )}
              
              <div className="px-6 py-4 border-t border-gray-200 flex space-x-3">
                <button
                  onClick={() => setShowQuickLog(false)}
                  disabled={isSaving}
                  className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  onClick={saveMoodEntry}
                  disabled={isSaving}
                  className="flex-1 px-4 py-2 bg-pink-600 text-white rounded-lg hover:bg-pink-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSaving ? 'Saving...' : 'Save Entry'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
